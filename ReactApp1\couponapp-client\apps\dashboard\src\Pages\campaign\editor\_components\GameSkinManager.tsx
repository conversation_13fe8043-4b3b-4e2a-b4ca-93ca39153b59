import React, { useState, useMemo } from 'react'
import { Widget } from '@repo/shared/lib/types/editor'
import { GameWidgetSettings } from '@repo/shared/components/react-templates/blocks/game'
import { useWidgetSettings } from '@/lib/hooks/useWidgetSettings'
import { Label } from '@repo/shared/components/ui/label'
import { Button } from '@repo/shared/components/ui/button'
import { Textarea } from '@repo/shared/components/ui/textarea'
import { skinsDirectory } from '@repo/shared/lib/game/gameSkinsTest'
import { applySkin, GameSkin, canApplySkin, getCompatibleSkins } from '@repo/shared/lib/game/gameSkin'
import { getGameModule, loadGameModule } from '@repo/shared/lib/game/gameRegistry'
import { getConfigByCategory } from '@repo/shared/lib/game/gameConfig'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { post } from '@repo/shared/lib/hooks/useApi'

interface GameSkinManagerProps {
    widget: Widget
}

interface AssetTransformationResult {
    originalConfig: any
    transformedConfig: any
    assetMappings: Array<{
        originalAssetId: string
        newAbsoluteUrl: string
        configPath: string
    }>
}

export const GameSkinManager: React.FC<GameSkinManagerProps> = ({ widget }) => {
    const { settings, updateSettings } = useWidgetSettings<GameWidgetSettings>(widget)
    const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false)
    const [selectedSkinId, setSelectedSkinId] = useState<string>('')
    const [isExportDialogOpen, setIsExportDialogOpen] = useState<boolean>(false)
    const [exportedSkinData, setExportedSkinData] = useState<string>('')
    const [isExporting, setIsExporting] = useState<boolean>(false)

    // Filter skins to only show those compatible with the current game
    const compatibleSkins = useMemo(() => {
        if (!settings?.gameId) return []
        return getCompatibleSkins(settings.gameId, skinsDirectory)
    }, [settings?.gameId])

    const handleApplySkin = (skinId: string) => {
        if (!skinId || !settings?.gameConfig || !settings?.gameId) {
            return
        }

        const selectedSkin = skinsDirectory.find(skin => skin.id === skinId)
        if (!selectedSkin) {
            return
        }

        // Validate that the skin can be applied to this game
        if (!canApplySkin(settings.gameId, selectedSkin)) {
            console.error(`Cannot apply skin "${selectedSkin.name}" to game "${settings.gameId}"`)
            return
        }

        try {
            const newConfig = applySkin(settings.gameConfig, selectedSkin, settings.gameId)
            updateSettings({
                gameConfig: newConfig,
            })
            setSelectedSkinId(skinId)
            setIsPopupOpen(false)
        } catch (error) {
            console.error('Failed to apply skin:', error)
        }
    }

    // Recursively scan for AssetUrl objects in the config
    const scanForAssetUrls = (obj: any, path: string = ''): Array<{ assetUrl: AssetUrl, path: string }> => {
        const results: Array<{ assetUrl: AssetUrl, path: string }> = []
        
        if (!obj || typeof obj !== 'object') {
            return results
        }

        // Check if current object is an AssetUrl
        if (obj.hasOwnProperty('assetId') || obj.hasOwnProperty('absoluteUrl')) {
            results.push({ assetUrl: obj as AssetUrl, path })
        }

        // Recursively scan nested objects and arrays
        for (const [key, value] of Object.entries(obj)) {
            const newPath = path ? `${path}.${key}` : key
            if (typeof value === 'object' && value !== null) {
                results.push(...scanForAssetUrls(value, newPath))
            }
        }

        return results
    }

    // Mock function to transform asset URLs (will be replaced with actual server call)
    // TODO: Replace with actual server call that:
    // 1. Takes organization asset IDs and copies them to public skin directory
    // 2. Returns absolute URLs for the copied assets
    const transformAssetUrls = async (assetUrls: Array<{ assetUrl: AssetUrl, path: string }>): Promise<AssetTransformationResult['assetMappings']> => {
        // Mock implementation - in real implementation this would call the server
        // Example server call: POST /api/skins/transform-assets { assetIds: [...] }
        const mappings: AssetTransformationResult['assetMappings'] = []

        for (const { assetUrl, path } of assetUrls) {
            if (assetUrl.assetId) {
                // Mock transformation - replace with actual server call
                const mockAbsoluteUrl = `https://example.com/public/skins/assets/${assetUrl.assetId}.png`
                mappings.push({
                    originalAssetId: assetUrl.assetId,
                    newAbsoluteUrl: mockAbsoluteUrl,
                    configPath: path
                })
            }
        }

        return mappings
    }

    // Apply asset transformations to the config
    const applyAssetTransformations = (config: any, mappings: AssetTransformationResult['assetMappings']): any => {
        const transformedConfig = JSON.parse(JSON.stringify(config)) // Deep clone

        for (const mapping of mappings) {
            const pathParts = mapping.configPath.split('.')
            let current = transformedConfig

            // Navigate to the parent object
            for (let i = 0; i < pathParts.length - 1; i++) {
                if (current[pathParts[i]]) {
                    current = current[pathParts[i]]
                }
            }

            // Update the asset URL
            const lastKey = pathParts[pathParts.length - 1]
            if (current[lastKey] && current[lastKey].assetId === mapping.originalAssetId) {
                // Create new object without assetId and with absoluteUrl
                const { assetId, ...restProps } = current[lastKey]
                current[lastKey] = {
                    ...restProps,
                    absoluteUrl: mapping.newAbsoluteUrl
                }
            }
        }

        return transformedConfig
    }

    const handleExportSkin = async () => {
        if (!settings?.gameConfig || !settings?.gameId) {
            return
        }

        setIsExporting(true)
        
        try {
            // Load the game module to get the config type
            let gameModule = getGameModule(settings.gameId)
            if (!gameModule) {
                await loadGameModule(settings.gameId)
                gameModule = getGameModule(settings.gameId)
            }

            if (!gameModule || !gameModule.configType) {
                console.error('Could not load game module or config type')
                return
            }

            // Extract appearance settings from current config
            const appearanceConfig = getConfigByCategory(settings.gameConfig, gameModule.configType, 'appearance')
            
            // Scan for asset URLs in the appearance config
            const assetUrls = scanForAssetUrls(appearanceConfig)
            console.log('Found asset URLs:', assetUrls)
            
            // Transform asset URLs (mock for now)
            const assetMappings = await transformAssetUrls(assetUrls)
            console.log('Asset mappings:', assetMappings)
            
            // Apply transformations to create the final config
            const transformedConfig = applyAssetTransformations(appearanceConfig, assetMappings)
            
            // Create the export result
            const exportResult: AssetTransformationResult = {
                originalConfig: appearanceConfig,
                transformedConfig,
                assetMappings
            }

            // Format as JSON string for display
            const exportData = JSON.stringify(exportResult, null, 2)
            setExportedSkinData(exportData)
            setIsExportDialogOpen(true)
        } catch (error) {
            console.error('Failed to export skin:', error)
        } finally {
            setIsExporting(false)
        }
    }

    return (
        <div className="space-y-2">
            <div className="flex items-center justify-between">
                <Label>Game Skins</Label>
                <Button
                    onClick={() => setIsPopupOpen(true)}
                    variant="secondary"
                    size="sm"
                >
                    Browse Skins
                </Button>
            </div>

            {selectedSkinId && (
                <div className="text-xs text-muted-foreground">
                    Applied: {compatibleSkins.find(s => s.id === selectedSkinId)?.name || skinsDirectory.find(s => s.id === selectedSkinId)?.name}
                </div>
            )}

            {isPopupOpen && (
                <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
                    <div className="bg-card border rounded-md shadow-lg max-w-xs w-full mx-4 max-h-[70vh] overflow-hidden">
                        <div className="flex items-center justify-between px-3 py-2 border-b">
                            <h3 className="text-sm font-medium">Choose Skin</h3>
                            <Button
                                onClick={() => setIsPopupOpen(false)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                            >
                                ×
                            </Button>
                        </div>

                        <div className="p-2 max-h-80 overflow-y-auto">
                            <div className="space-y-1">
                                {compatibleSkins.length === 0 ? (
                                    <div className="text-center text-muted-foreground text-sm py-4">
                                        No skins available for this game
                                    </div>
                                ) : (
                                    compatibleSkins.map((skin) => (
                                        <button
                                            key={skin.id}
                                            onClick={() => handleApplySkin(skin.id)}
                                            className={`w-full text-left px-2 py-1.5 rounded text-sm transition-colors ${
                                                selectedSkinId === skin.id
                                                    ? 'bg-primary/10 text-primary'
                                                    : 'hover:bg-accent/50'
                                            }`}
                                        >
                                            <div className="flex items-center justify-between">
                                                <span className="truncate">{skin.name}</span>
                                                {selectedSkinId === skin.id && (
                                                    <span className="text-primary text-xs ml-2">✓</span>
                                                )}
                                            </div>
                                        </button>
                                    ))
                                )}
                            </div>
                        </div>

                        <div className="border-t px-2 py-2 space-y-1">
                            <Button
                                onClick={handleExportSkin}
                                variant="secondary"
                                size="sm"
                                className="w-full text-xs"
                                disabled={isExporting}
                            >
                                {isExporting ? 'Exporting...' : 'Export Skin'}
                            </Button>
                            <Button
                                onClick={() => setIsPopupOpen(false)}
                                variant="ghost"
                                size="sm"
                                className="w-full text-xs"
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {isExportDialogOpen && (
                <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
                    <div className="bg-card border rounded-md shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
                        <div className="flex items-center justify-between px-3 py-2 border-b">
                            <h3 className="text-sm font-medium">Export Skin</h3>
                            <Button
                                onClick={() => setIsExportDialogOpen(false)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                            >
                                ×
                            </Button>
                        </div>

                        <div className="p-3">
                            <p className="text-xs text-muted-foreground mb-2">
                                Skin export with asset URL transformations (mock data):
                            </p>
                            <Textarea
                                value={exportedSkinData}
                                readOnly
                                className="w-full h-96 text-xs font-mono resize-none"
                                placeholder="Exported skin data will appear here..."
                            />
                        </div>

                        <div className="border-t px-3 py-2">
                            <Button
                                onClick={() => setIsExportDialogOpen(false)}
                                variant="ghost"
                                size="sm"
                                className="w-full text-xs"
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

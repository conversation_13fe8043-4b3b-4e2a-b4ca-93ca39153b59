import { GameWidget, GameWidgetSettings } from '@repo/shared/components/react-templates/blocks/game'
import { MarginControl, PaddingControl, SizeControl } from './shared/layout-controls'
import { addPropertyControls } from '../../property-controls'
import { Label } from '@repo/shared/components/ui/label'
import { Widget } from '@repo/shared/lib/types/editor'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { BorderRadiusControl } from './shared/border-controls'
import { getGameModule, loadGameModule, gameModulesList } from '@repo/shared/lib/game/gameRegistry'
import { useState, useEffect } from 'react'
import { Suspense } from 'react'
import { useWidgetSettings } from '@/lib/hooks/useWidgetSettings'
import { GameSkinManager } from '../GameSkinManager'

function GameTypeControl(props: { widget: Widget }) {
    const games = gameModulesList
    const { settings, updateSettings } = useWidgetSettings<GameWidgetSettings>(props.widget)
    const [currentGame, setCurrentGame] = useState(settings?.gameId ?? '2048')

    const handleGameChange = async (value: string) => {
        setCurrentGame(value)

        let gameModule = getGameModule(value)
        if (gameModule == null) {
            await loadGameModule(value)
            gameModule = getGameModule(value)
        }

        //TODO batch update of these!
        updateSettings({
            gameId: value,
            gameConfig: gameModule.defaultConfig,
        })
    }

    return (
        <div className="space-y-2">
            <Label>Game</Label>
            <Select value={currentGame} onValueChange={handleGameChange}>
                <SelectTrigger>
                    <SelectValue />
                </SelectTrigger>
                <SelectContent>
                    {games.map((game) => (
                        <SelectItem key={game} value={game}>
                            {game}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    )
}

function GameSettingsControl(props: { widget: Widget }) {
    const { settings, updateSettings } = useWidgetSettings<GameWidgetSettings>(props.widget)
    const [error, setError] = useState<Error | null>(null)

    const [EditorComponent, setEditorComponent] = useState<any>(null)

    useEffect(() => {
        const loadEditor = async () => {
            try {
                const gameModule = await loadGameModule(settings?.gameId)
                if (gameModule && gameModule.editorComponent) {
                    setEditorComponent(() => gameModule.editorComponent)
                }
            } catch (err) {
                setError(err instanceof Error ? err : new Error('Failed to load game editor'))
                console.error('Failed to load game editor:', err)
            }
        }

        loadEditor()
    }, [settings?.gameId])

    const updateConfig = (config: any) => {
        updateSettings({
            gameConfig: config,
        })
    }

    if (error) {
        return (
            <div className="space-y-2">
                <div className="text-red-500">Error loading game editor: {error.message}</div>
            </div>
        )
    }

    return (
        <div className="space-y-4">
            <Suspense fallback={<div>Loading game settings editor...</div>}>{EditorComponent && <EditorComponent config={settings?.gameConfig} updateConfig={updateConfig} />}</Suspense>
        </div>
    )
}

function SkinSelectorControl(props: { widget: Widget }) {
    return <GameSkinManager widget={props.widget} />
}

function SizeControlGameWidget({ widget }: { widget: Widget }) {
    return <SizeControl widget={widget} availableSizeModes={['fixed', 'fluid']} />
}

addPropertyControls(GameWidget, {
    skins: {
        name: 'Skins',
        controls: [SkinSelectorControl],
    },
    game: {
        name: 'Game',
        controls: [GameTypeControl],
    },
    gameSpecific: {
        name: 'Game Specific',
        controls: [GameSettingsControl],
    },
    layout: {
        name: 'Layout',
        controls: [SizeControlGameWidget, MarginControl, PaddingControl],
    },
    appearance: {
        name: 'Appearance',
        controls: [BorderRadiusControl],
    },
})
